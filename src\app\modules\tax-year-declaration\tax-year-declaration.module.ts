import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import {
  SeAlertModule,
  SeButtonModule,
  SeDatepickerModule,
  SeDropdownModule,
  SeInputModule,
  SePanelModule,
  SeSwitchModule,
  SeTableModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { TaxYearDeclarationComponent } from './tax-year-declaration.component';
import { environment } from '@environments/environment';
import { SubstitutivePanelComponent } from './substitutive/substitutive-panel.component';
import { InsurancePanelComponent } from './insurance/insurance-panel.component';
import { SafeHtmlPipe } from './services/safe-html.pipe';

const routes: Routes = [
  {
    path: '',
    component: TaxYearDeclarationComponent,
    data: {
      title: 'SE_DECINF_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP2',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [SafeHtmlPipe],
  declarations: [
    TaxYearDeclarationComponent,
    SubstitutivePanelComponent,
    InsurancePanelComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-upload-files',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-documents-docs-actions',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    SeButtonModule,
    SeDropdownModule,
    SePanelModule,
    SeAlertModule,
    SeSwitchModule,
    SeTableModule,
    SeInputModule,
    SeDatepickerModule,
  ],
})
export class TaxYearDeclarationModule {}
